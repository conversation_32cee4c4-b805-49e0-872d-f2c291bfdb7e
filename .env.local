# Local Configuration for Full Offline Operation
# OpenAI Configuration (Optional - LM Studio is primary)
OPENAI_API_KEY=

# Local LM Studio Configuration (Primary AI Engine)
LM_STUDIO_URL=http://*************:1234
USE_LOCAL_LLM=true
LOCAL_LLM_FALLBACK=true

# Model Configuration
MODEL_CHOICE=local-gemma3-4b
LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2

# External APIs (Optional - for web research only)
TAVILY_API_KEY=
FIRECRAWL_API_KEY=

# Database Configuration (Local Docker)
CHROMA_DB_HOST=chromadb
CHROMA_DB_PORT=8000
REDIS_HOST=redis
REDIS_PORT=6379

# MCP Server Configuration
MCP_SERVER_PORT=8978

# Workspace Configuration
WORKSPACE_ROOT=/workspace
INDEXING_INTERVAL=300

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
SECRET_KEY=local-development-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,*************

# Performance Configuration (Optimized for Local)
MAX_CONCURRENT_CRAWLS=5
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_FILE_SIZE=1048576
BATCH_SIZE=50

# Local Processing Configuration
ENABLE_LOCAL_PROCESSING=true
OFFLINE_MODE=true
LOCAL_CACHE_SIZE=1000

# HVAC Project Integration (Local Development)
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1
MINIO_BUCKET=hvac-documents

# MongoDB Configuration (External)
MONGODB_URL=***************************************************************

# PostgreSQL Configuration (Local Development)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=hvac_crm_local
POSTGRES_USER=postgres
POSTGRES_PASSWORD=local_dev_password

# Local Development Features
ENABLE_DEBUG=true
ENABLE_HOT_RELOAD=true
ENABLE_PROFILING=false
ENABLE_METRICS=true

# Network Configuration
NETWORK_TIMEOUT=30
CONNECTION_POOL_SIZE=10
MAX_RETRIES=3

# File Processing Configuration
SUPPORTED_EXTENSIONS=.py,.js,.ts,.go,.rs,.java,.cpp,.c,.h,.md,.txt,.rst,.yaml,.yml,.json,.xml,.html,.css,.sql,.sh,.bash,.dockerfile,.env,.gitignore,.toml,.ini
IGNORE_PATTERNS=__pycache__,.git,node_modules,.venv,venv,.env,dist,build,.next,.nuxt,target,bin,obj

# AI Processing Configuration
DEFAULT_TEMPERATURE=0.7
DEFAULT_MAX_TOKENS=2048
CONTEXT_WINDOW_SIZE=4096
ENABLE_STREAMING=false

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=30
METRICS_PORT=9090
ENABLE_PROMETHEUS=true
