from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum

class TranscriptionResultSchema(BaseModel):
    text: str = Field(..., description="The transcribed text.")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score of the transcription (0.0 to 1.0).")
    hvac_keywords: List[str] = Field(default_factory=list, description="List of HVAC keywords detected in the transcription.")
    audio_quality: str = Field("unknown", description="Assessment of the audio quality (e.g., 'good', 'poor', 'unknown').")
    model_used: str = Field("", description="Name or identifier of the STT model used for transcription.")

    class Config:
        extra = "ignore" # Ignore extra fields not defined in the schema

class PowerState(str, Enum):
    OFF = "off"
    STANDBY = "standby"
    ON = "on"

class PowerSettings(BaseModel):
    auto_optimize: bool = Field(True, description="Automatically optimize power usage when possible")
    max_power: int = Field(5000, description="Maximum power consumption in watts")
    min_power: int = Field(0, description="Minimum power consumption in watts")
    default_state: PowerState = Field(PowerState.STANDBY, description="Default power state")