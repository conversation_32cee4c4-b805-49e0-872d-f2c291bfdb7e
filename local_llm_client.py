#!/usr/bin/env python3
"""
Local LLM Client for LM Studio Integration
Provides full local AI capabilities without external API dependencies
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Union
import httpx
import structlog

logger = structlog.get_logger()

class LocalLLMClient:
    """Client for local LM Studio integration with full offline capabilities"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or os.getenv("LM_STUDIO_URL", "http://192.168.0.179:1234")
        self.client = httpx.AsyncClient(timeout=300.0)  # 5 minute timeout for large responses
        self.available_models = []
        self.current_model = None
        
    async def initialize(self):
        """Initialize connection and discover available models"""
        try:
            # Test connection
            response = await self.client.get(f"{self.base_url}/v1/models")
            if response.status_code == 200:
                models_data = response.json()
                self.available_models = [model["id"] for model in models_data.get("data", [])]
                if self.available_models:
                    self.current_model = self.available_models[0]
                    logger.info("LM Studio connected", 
                               models=self.available_models, 
                               current=self.current_model)
                else:
                    logger.warning("No models loaded in LM Studio")
            else:
                logger.error("Failed to connect to LM Studio", status=response.status_code)
        except Exception as e:
            logger.error("LM Studio connection failed", error=str(e))
            # Fallback to offline mode
            self.current_model = "offline-mode"
    
    async def generate_text(self, 
                           prompt: str, 
                           max_tokens: int = 2048,
                           temperature: float = 0.7,
                           system_prompt: str = None) -> str:
        """Generate text using local LM Studio model"""
        try:
            if not self.current_model or self.current_model == "offline-mode":
                return self._offline_fallback(prompt)
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            payload = {
                "model": self.current_model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }
            
            response = await self.client.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                logger.error("LM Studio generation failed", status=response.status_code)
                return self._offline_fallback(prompt)
                
        except Exception as e:
            logger.error("Text generation failed", error=str(e))
            return self._offline_fallback(prompt)
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using local model or fallback to sentence-transformers"""
        try:
            # Try LM Studio embeddings endpoint first
            if self.current_model and self.current_model != "offline-mode":
                payload = {
                    "model": self.current_model,
                    "input": texts
                }
                
                response = await self.client.post(
                    f"{self.base_url}/v1/embeddings",
                    json=payload
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return [item["embedding"] for item in result["data"]]
            
            # Fallback to local sentence-transformers
            return await self._local_embeddings(texts)
            
        except Exception as e:
            logger.error("Embedding generation failed", error=str(e))
            return await self._local_embeddings(texts)
    
    async def _local_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using sentence-transformers locally"""
        try:
            from sentence_transformers import SentenceTransformer
            
            # Use lightweight model for embeddings
            model = SentenceTransformer('all-MiniLM-L6-v2')
            embeddings = model.encode(texts)
            return embeddings.tolist()
            
        except Exception as e:
            logger.error("Local embeddings failed", error=str(e))
            # Return dummy embeddings as last resort
            return [[0.0] * 384 for _ in texts]
    
    def _offline_fallback(self, prompt: str) -> str:
        """Offline fallback for when LM Studio is unavailable"""
        # Simple rule-based responses for common queries
        prompt_lower = prompt.lower()
        
        if "search" in prompt_lower or "find" in prompt_lower:
            return "Local search functionality available. Use workspace indexing for file search."
        elif "code" in prompt_lower or "function" in prompt_lower:
            return "Code analysis available through workspace indexing. Check indexed files for relevant code."
        elif "hvac" in prompt_lower:
            return "HVAC CRM system information available through local database queries."
        elif "help" in prompt_lower or "how" in prompt_lower:
            return "Local MCP server running. Available tools: search_workspace, crawl_website, store_memory, retrieve_memory."
        else:
            return f"Local processing of: {prompt[:100]}... (LM Studio offline - using local capabilities)"
    
    async def analyze_code(self, code: str, language: str = "python") -> Dict[str, Any]:
        """Analyze code using local capabilities"""
        analysis = {
            "language": language,
            "lines": len(code.split('\n')),
            "characters": len(code),
            "functions": [],
            "classes": [],
            "imports": [],
            "complexity": "medium"
        }
        
        # Simple static analysis
        lines = code.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('def '):
                analysis["functions"].append(line.split('(')[0].replace('def ', ''))
            elif line.startswith('class '):
                analysis["classes"].append(line.split('(')[0].replace('class ', '').replace(':', ''))
            elif line.startswith('import ') or line.startswith('from '):
                analysis["imports"].append(line)
        
        # Use LM Studio for deeper analysis if available
        if self.current_model and self.current_model != "offline-mode":
            try:
                prompt = f"""Analyze this {language} code and provide insights:

{code}

Please provide:
1. Code quality assessment
2. Potential improvements
3. Security considerations
4. Performance notes"""
                
                llm_analysis = await self.generate_text(prompt, max_tokens=1024)
                analysis["llm_insights"] = llm_analysis
            except Exception as e:
                logger.error("LLM code analysis failed", error=str(e))
        
        return analysis
    
    async def summarize_text(self, text: str, max_length: int = 200) -> str:
        """Summarize text using local capabilities"""
        if len(text) <= max_length:
            return text
        
        # Try LM Studio first
        if self.current_model and self.current_model != "offline-mode":
            try:
                prompt = f"Summarize this text in {max_length} characters or less:\n\n{text}"
                return await self.generate_text(prompt, max_tokens=max_length//4)
            except Exception:
                pass
        
        # Fallback to simple truncation with sentence boundary
        if len(text) <= max_length:
            return text
        
        truncated = text[:max_length]
        last_sentence = truncated.rfind('.')
        if last_sentence > max_length // 2:
            return truncated[:last_sentence + 1]
        else:
            return truncated + "..."
    
    async def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Extract keywords from text using local processing"""
        # Simple keyword extraction
        import re
        from collections import Counter
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        filtered_words = [word for word in words if word not in stop_words]
        
        # Count frequency
        word_counts = Counter(filtered_words)
        keywords = [word for word, count in word_counts.most_common(max_keywords)]
        
        return keywords
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Global instance
local_llm = LocalLLMClient()

async def initialize_local_llm():
    """Initialize the global LLM client"""
    await local_llm.initialize()

# Convenience functions
async def generate_local_text(prompt: str, **kwargs) -> str:
    """Generate text using local LLM"""
    return await local_llm.generate_text(prompt, **kwargs)

async def analyze_local_code(code: str, language: str = "python") -> Dict[str, Any]:
    """Analyze code using local capabilities"""
    return await local_llm.analyze_code(code, language)

async def summarize_local_text(text: str, max_length: int = 200) -> str:
    """Summarize text locally"""
    return await local_llm.summarize_text(text, max_length)

async def extract_local_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """Extract keywords locally"""
    return await local_llm.extract_keywords(text, max_keywords)
