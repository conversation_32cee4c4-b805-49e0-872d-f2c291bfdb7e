import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { createClient } from "redis";
import { z } from "zod";

// Environment variables
const GOSPINE_API_URL = process.env['GOSPINE_API_URL'] || "http://localhost:8080/api/customers";
const GOSPINE_API_KEY = process.env['GOSPINE_API_KEY'];
const REDIS_URL = process.env['REDIS_URL'] || "redis://localhost:6379";

// Zod schema for query parameters
const QueryParamsSchema = z.object({
  customerId: z.string().optional(),
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().max(100).optional().default(20),
  search: z.string().optional()
});

// GoSpine customer data structure
export interface GoSpineCustomerData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  serviceHistory: {
    serviceId: string;
    date: string;
    description: string;
    status: string;
  }[];
}

// Create Redis client
const redisClient = createClient({ url: REDIS_URL });
redisClient.on("error", (err) => console.error("Redis Client Error", err));
redisClient.connect();

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const queryParams = QueryParamsSchema.parse({
      customerId: url.searchParams.get("customerId"),
      page: url.searchParams.get("page"),
      limit: url.searchParams.get("limit"),
      search: url.searchParams.get("search")
    });

    // Generate cache key
    const cacheKey = `gospine:customers:${JSON.stringify(queryParams)}`;

    // Try to get cached data
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
      return json({ success: true, data: JSON.parse(cachedData), cached: true });
    }

    // Build GoSpine API URL
    const goSpineUrl = new URL(GOSPINE_API_URL);
    if (queryParams.customerId) {
      goSpineUrl.pathname += `/${queryParams.customerId}`;
    } else {
      goSpineUrl.searchParams.append("page", queryParams.page.toString());
      goSpineUrl.searchParams.append("limit", queryParams.limit.toString());
      if (queryParams.search) {
        goSpineUrl.searchParams.append("search", queryParams.search);
      }
    }

    // Fetch data from GoSpine backend with authentication
    const response = await fetch(goSpineUrl.toString(), {
      headers: {
        "Authorization": `Bearer ${GOSPINE_API_KEY}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching GoSpine customer data: ${response.status} - ${errorText}`);
      return json(
        {
          error: "Failed to fetch customer data from GoSpine",
          status: response.status,
          details: response.statusText
        },
        { status: response.status }
      );
    }

    const customerData: GoSpineCustomerData | GoSpineCustomerData[] = await response.json();

    // Cache the response for 5 minutes
    await redisClient.setEx(cacheKey, 300, JSON.stringify(customerData));

    return json({ success: true, data: customerData });
  } catch (error: any) {
    console.error("Error in GoSpine customer API route:", error);
    
    if (error instanceof z.ZodError) {
      return json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }
    
    return json(
      {
        error: "Internal server error while fetching GoSpine customer data",
        details: error.message
      },
      { status: 500 }
    );
  }
};