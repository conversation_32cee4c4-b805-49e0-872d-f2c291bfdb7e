import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useNavigation, useSearchParams } from "@remix-run/react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { ExclamationTriangleIcon, ReloadIcon } from "@radix-ui/react-icons";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Skeleton } from "~/components/ui/skeleton";
import type { GoSpineCustomerData } from "~/routes/api.go-spine.customers";

export const meta: MetaFunction = () => {
  return [{ title: "GoSpine Customers | HVAC CRM" }];
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");
  const page = url.searchParams.get("page") || "1";
  const limit = url.searchParams.get("limit") || "20";
  const search = url.searchParams.get("search") || "";

  try {
    const apiUrl = new URL("/api/go-spine.customers", url.origin);
    if (customerId) apiUrl.searchParams.append("customerId", customerId);
    apiUrl.searchParams.append("page", page);
    apiUrl.searchParams.append("limit", limit);
    if (search) apiUrl.searchParams.append("search", search);

    const response = await fetch(apiUrl.toString());
    const data = await response.json();

    if (!response.ok) {
      return json({
        error: data.error || "Failed to load customer data",
        status: response.status,
        details: data.details
      }, { status: response.status });
    }

    return json({
      customers: data.data as GoSpineCustomerData[],
      cached: data.cached || false,
      total: data.total || 0,
      page: parseInt(page),
      limit: parseInt(limit),
      search
    });
  } catch (error: any) {
    console.error("Error fetching GoSpine customer data:", error);
    return json({
      error: "Failed to load customer data",
      details: error.message
    }, { status: 500 });
  }
};

export default function GoSpineCustomersPage() {
  const loaderData = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const isLoading = navigation.state === "loading";
  const customers = 'customers' in loaderData ? loaderData.customers : undefined;
  const error = 'error' in loaderData ? loaderData.error : undefined;
  const currentPage = loaderData.page || 1;
  const totalPages = Math.ceil((loaderData.total || 0) / (loaderData.limit || 20));
  const searchQuery = searchParams.get("search") || "";

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const search = formData.get("search") as string;
    const newParams = new URLSearchParams(searchParams);
    newParams.set("search", search);
    newParams.delete("page");
    setSearchParams(newParams);
  };

  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error Loading Customer Data</AlertTitle>
          <AlertDescription>
            {error.message} {error.details && `(${error.details})`}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-3xl font-bold">GoSpine Customers</h1>
        
        <form onSubmit={handleSearch} className="flex gap-2">
          <Input
            name="search"
            placeholder="Search customers..."
            defaultValue={searchQuery}
            className="max-w-md"
          />
          <Button type="submit">Search</Button>
        </form>
      </div>

      {loaderData.cached && (
        <div className="bg-yellow-100 text-yellow-800 p-3 rounded-md flex items-center gap-2">
          <ReloadIcon className="h-4 w-4" />
          <span>Showing cached data</span>
        </div>
      )}

      {isLoading && !customers && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
              </CardHeader>
              <CardContent className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5" />
                <Skeleton className="h-4 w-5/6" />
                <div className="mt-4 space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {!isLoading && (!customers || customers.length === 0) ? (
        <div className="p-4">
          <Alert>
            <AlertTitle>No Data</AlertTitle>
            <AlertDescription>
              No customer data found from GoSpine.
            </AlertDescription>
          </Alert>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {customers?.map((customer) => (
              <Card key={customer.id} className="transition-all hover:shadow-lg hover:border-blue-300">
                <CardHeader className="flex flex-row items-start gap-4">
                  <div className="bg-blue-100 text-blue-800 rounded-full w-12 h-12 flex items-center justify-center font-bold text-lg">
                    {customer.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <CardTitle>{customer.name}</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">{customer.email}</p>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p>{customer.phone}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Address</p>
                      <p className="line-clamp-1">{customer.address}</p>
                    </div>
                  </div>
                  
                  {customer.serviceHistory && customer.serviceHistory.length > 0 && (
                    <div className="mt-4 border-t pt-4">
                      <details>
                        <summary className="font-semibold text-blue-600 cursor-pointer">Service History ({customer.serviceHistory.length})</summary>
                        <ul className="mt-2 space-y-3">
                          {customer.serviceHistory.map((service) => (
                            <li key={service.serviceId} className="border-l-2 pl-2 border-blue-500">
                              <div className="font-medium">{service.date}</div>
                              <div>{service.description}</div>
                              <div className="text-sm text-muted-foreground">Status: {service.status}</div>
                            </li>
                          ))}
                        </ul>
                      </details>
                    </div>
                  )}
                  
                  <div className="mt-4">
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-8">
              <Button
                variant="outline"
                disabled={currentPage <= 1}
                onClick={() => handlePageChange(currentPage - 1)}
              >
                Previous
              </Button>
              
              <span className="flex items-center px-4">
                Page {currentPage} of {totalPages}
              </span>
              
              <Button
                variant="outline"
                disabled={currentPage >= totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}