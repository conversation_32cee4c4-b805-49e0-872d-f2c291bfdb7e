import { useRouteError, isRouteErrorResponse, Link } from '@remix-run/react';
import { AlertCircle, Home, RefreshCw } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '~/components/ui/card';
import { captureException } from '~/utils/monitoring.client';

/**
 * Root error boundary component
 * 
 * This component handles errors at the application level.
 * It displays a user-friendly error message and provides options to recover.
 */
export default function RootErrorBoundary() {
  const error = useRouteError();
  
  // Determine if this is a known route error (e.g., 404, 401, etc.)
  const isRouteError = isRouteErrorResponse(error);
  
  // Capture unexpected errors with Sentry
  if (!isRouteError) {
    if (error instanceof Error) {
      captureException(error);
    } else {
      // If it's not a route error and not an Error instance, capture it as a generic error message
      captureException(new Error(`Unknown non-route error: ${JSON.stringify(error)}`));
    }
  }

  // Get error details
  const errorStatus = isRouteError ? error.status : 500;
  const errorMessage = isRouteError
    ? error.data
    : error instanceof Error
    ? error.message
    : 'Unknown error occurred';
  
  // Get a user-friendly title based on the error status
  const errorTitle = getErrorTitle(errorStatus);
  
  // Get a user-friendly description based on the error status
  const errorDescription = getErrorDescription(errorStatus);
  
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-slate-50 dark:bg-slate-900">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className={getHeaderClass(errorStatus)}>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <CardTitle>{errorTitle}</CardTitle>
          </div>
          <CardDescription className="text-slate-700 dark:text-slate-300">
            {errorDescription}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="pt-6">
          <div className="text-sm text-muted-foreground mb-4">
            <p>Error details:</p>
            <pre className="mt-2 p-4 bg-slate-100 dark:bg-slate-900 rounded-md overflow-auto text-xs">
              {errorStatus}: {errorMessage}
            </pre>
          </div>
          
          <p className="text-sm text-muted-foreground">
            You can try refreshing the page or returning to the home page.
          </p>
        </CardContent>
        
        <CardFooter className="flex justify-between gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Page
          </Button>
          
          <Button
            variant="default"
            className="flex-1"
            asChild
          >
            <Link to="/">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

/**
 * Get a user-friendly error title based on the error status
 */
function getErrorTitle(status: number): string {
  switch (status) {
    case 400:
      return 'Bad Request';
    case 401:
      return 'Unauthorized';
    case 403:
      return 'Forbidden';
    case 404:
      return 'Page Not Found';
    case 500:
      return 'Server Error';
    case 503:
      return 'Service Unavailable';
    default:
      return 'An Error Occurred';
  }
}

/**
 * Get a user-friendly error description based on the error status
 */
function getErrorDescription(status: number): string {
  switch (status) {
    case 400:
      return 'The request could not be understood by the server due to malformed syntax.';
    case 401:
      return 'You need to be logged in to access this page.';
    case 403:
      return 'You do not have permission to access this page.';
    case 404:
      return 'The page you are looking for does not exist or has been moved.';
    case 500:
      return 'Something went wrong on our end. We\'re working to fix the issue.';
    case 503:
      return 'The service is temporarily unavailable. Please try again later.';
    default:
      return 'An unexpected error occurred while processing your request.';
  }
}

/**
 * Get the appropriate CSS class for the error header based on the error status
 */
function getHeaderClass(status: number): string {
  // Base classes
  const baseClass = 'border-b';
  
  // Status-specific classes
  switch (status) {
    case 400:
    case 401:
    case 403:
      return `${baseClass} bg-yellow-50 dark:bg-yellow-900/20 border-yellow-100 dark:border-yellow-900/30`;
    case 404:
      return `${baseClass} bg-blue-50 dark:bg-blue-900/20 border-blue-100 dark:border-blue-900/30`;
    case 500:
    case 503:
    default:
      return `${baseClass} bg-red-50 dark:bg-red-900/20 border-red-100 dark:border-red-900/30`;
  }
}
