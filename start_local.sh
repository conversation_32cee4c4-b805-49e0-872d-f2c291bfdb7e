#!/bin/bash

# Enhanced Crawl4AI MCP Server - Local Startup Script
# Optimized for full local operation with LM Studio

set -e

echo "🚀 Starting Enhanced Crawl4AI MCP Server (Local Mode)"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if LM Studio is running
check_lm_studio() {
    print_status "Checking LM Studio connection..."
    
    if curl -s -f "http://*************:1234/v1/models" > /dev/null 2>&1; then
        print_success "LM Studio is running and accessible"
        
        # Get available models
        models=$(curl -s "http://*************:1234/v1/models" | jq -r '.data[].id' 2>/dev/null || echo "")
        if [ -n "$models" ]; then
            print_success "Available models:"
            echo "$models" | while read -r model; do
                echo "  - $model"
            done
        else
            print_warning "No models loaded in LM Studio"
        fi
    else
        print_warning "LM Studio not accessible at http://*************:1234"
        print_warning "System will run in offline mode with local processing"
    fi
}

# Check Docker and Docker Compose
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    print_success "Docker is ready"
}

# Setup local environment
setup_environment() {
    print_status "Setting up local environment..."
    
    # Copy local config if .env doesn't exist
    if [ ! -f .env ]; then
        if [ -f .env.local ]; then
            cp .env.local .env
            print_success "Copied .env.local to .env"
        else
            cp .env.example .env
            print_warning "Created .env from .env.example - please configure as needed"
        fi
    fi
    
    # Create necessary directories
    mkdir -p ./chroma_db ./data ./logs
    print_success "Created necessary directories"
}

# Build and start services
start_services() {
    print_status "Building and starting services..."
    
    # Stop any existing containers
    docker-compose down 2>/dev/null || true
    
    # Build with no cache for fresh start
    if [ "$1" = "--rebuild" ]; then
        print_status "Rebuilding containers from scratch..."
        docker-compose build --no-cache
    else
        docker-compose build
    fi
    
    # Start services
    docker-compose up -d
    
    print_success "Services started in background"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for ChromaDB
    print_status "Waiting for ChromaDB..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s -f "http://localhost:8000/api/v1/heartbeat" > /dev/null 2>&1; then
            print_success "ChromaDB is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "ChromaDB failed to start within 60 seconds"
        return 1
    fi
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker exec crawl4ai-redis redis-cli ping > /dev/null 2>&1; then
            print_success "Redis is ready"
            break
        fi
        sleep 1
        timeout=$((timeout - 1))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Redis failed to start within 30 seconds"
        return 1
    fi
    
    # Wait for MCP Server
    print_status "Waiting for MCP Server..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -s -f "http://localhost:8978/health" > /dev/null 2>&1; then
            print_success "MCP Server is ready"
            break
        fi
        sleep 3
        timeout=$((timeout - 3))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "MCP Server failed to start within 120 seconds"
        return 1
    fi
}

# Show service status
show_status() {
    print_status "Service Status:"
    echo ""
    
    # Service URLs
    echo "🌐 Service URLs:"
    echo "  • MCP Server:          http://localhost:8978"
    echo "  • MCP Health Check:    http://localhost:8978/health"
    echo "  • ChromaDB:            http://localhost:8000"
    echo "  • Streamlit Dashboard: http://localhost:8502"
    echo "  • LM Studio:           http://*************:1234"
    echo ""
    
    # Container status
    echo "🐳 Container Status:"
    docker-compose ps
    echo ""
    
    # MCP Tools Available
    echo "🔧 Available MCP Tools:"
    echo "  • search_workspace     - Semantic search in workspace files"
    echo "  • crawl_website        - Web crawling and indexing"
    echo "  • store_memory         - Persistent memory storage"
    echo "  • retrieve_memory      - Memory retrieval"
    echo "  • index_workspace      - Manual workspace indexing"
    echo "  • analyze_code_local   - Local code analysis with LM Studio"
    echo "  • generate_text_local  - Local text generation"
    echo "  • summarize_content    - Local content summarization"
    echo "  • extract_keywords     - Local keyword extraction"
    echo ""
}

# Show logs
show_logs() {
    print_status "Recent logs from MCP Server:"
    docker-compose logs --tail=20 crawl4ai-agent
}

# Main execution
main() {
    case "${1:-start}" in
        "start")
            check_docker
            check_lm_studio
            setup_environment
            start_services
            wait_for_services
            show_status
            ;;
        "rebuild")
            check_docker
            check_lm_studio
            setup_environment
            start_services --rebuild
            wait_for_services
            show_status
            ;;
        "stop")
            print_status "Stopping services..."
            docker-compose down
            print_success "Services stopped"
            ;;
        "restart")
            print_status "Restarting services..."
            docker-compose restart
            wait_for_services
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "clean")
            print_status "Cleaning up..."
            docker-compose down -v
            docker system prune -f
            print_success "Cleanup completed"
            ;;
        *)
            echo "Usage: $0 {start|rebuild|stop|restart|status|logs|clean}"
            echo ""
            echo "Commands:"
            echo "  start   - Start all services (default)"
            echo "  rebuild - Rebuild and start all services"
            echo "  stop    - Stop all services"
            echo "  restart - Restart all services"
            echo "  status  - Show service status and URLs"
            echo "  logs    - Show recent logs"
            echo "  clean   - Stop services and clean up volumes"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
