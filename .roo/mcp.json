{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/memory": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "transportType": "stdio"}, "github.com/upstash/context7-mcp": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@upstash/context7-mcp"], "transportType": "stdio"}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "env": {}}, "tavily": {"command": "npx", "args": ["-y", "tavily-mcp@0.2.1"], "env": {"TAVILY_API_KEY": "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"}}}}