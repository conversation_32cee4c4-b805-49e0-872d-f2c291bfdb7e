# The transport for the MCP server - either 'sse' or 'stdio' (defaults to sse if left empty)
TRANSPORT=

# Host to bind to if using sse as the transport (leave empty if using stdio)
HOST=

# Port to listen on if using sse as the transport (leave empty if using stdio)
PORT=

# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# This is for the embedding model - text-embed-small-3 will be used
OPENAI_API_KEY=

# The LLM you want to use for contextual embeddings (contextual retrieval)
# Leave this blank if you do not want to use contextual embeddings
# Generally this is a very cheap and fast LLM like gpt-4.1-nano
MODEL_CHOICE=

# For the Supabase version (sample_supabase_agent.py), set your Supabase URL and Service Key.
# Get your SUPABASE_URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=