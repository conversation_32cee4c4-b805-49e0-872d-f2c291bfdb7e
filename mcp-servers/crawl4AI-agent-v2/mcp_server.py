#!/usr/bin/env python3
"""
Enhanced MCP Server for Crawl4AI Agent with Workspace Indexing and Web Crawling
Provides comprehensive AI agent capabilities through Model Context Protocol
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional, Sequence
from pathlib import Path

import chromadb
import redis
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
import structlog
from local_llm_client import local_llm, initialize_local_llm

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

class EnhancedMCPServer:
    """Enhanced MCP Server with workspace indexing and web crawling capabilities"""
    
    def __init__(self):
        self.server = Server("crawl4ai-enhanced-mcp")
        self.app = FastAPI(title="Crawl4AI Enhanced MCP Server")
        self.setup_cors()
        self.setup_connections()
        self.setup_handlers()
        self.llm_initialized = False
        
    def setup_cors(self):
        """Setup CORS middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_connections(self):
        """Setup database connections"""
        try:
            # ChromaDB connection
            chroma_host = os.getenv("CHROMA_DB_HOST", "localhost")
            chroma_port = int(os.getenv("CHROMA_DB_PORT", "8000"))
            self.chroma_client = chromadb.HttpClient(
                host=chroma_host,
                port=chroma_port
            )
            
            # Redis connection
            redis_host = os.getenv("REDIS_HOST", "localhost")
            redis_port = int(os.getenv("REDIS_PORT", "6379"))
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                decode_responses=True
            )
            
            logger.info("Database connections established")
            
        except Exception as e:
            logger.error("Failed to setup database connections", error=str(e))
            raise
    
    def setup_handlers(self):
        """Setup MCP handlers"""
        
        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            """List available resources"""
            return [
                Resource(
                    uri="workspace://index",
                    name="Workspace Index",
                    description="Indexed workspace files and content",
                    mimeType="application/json"
                ),
                Resource(
                    uri="web://crawled",
                    name="Web Crawled Content",
                    description="Crawled web content and documentation",
                    mimeType="application/json"
                ),
                Resource(
                    uri="memory://context",
                    name="Context Memory",
                    description="Persistent context and memory storage",
                    mimeType="application/json"
                )
            ]
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Read resource content"""
            try:
                if uri.startswith("workspace://"):
                    return await self.read_workspace_resource(uri)
                elif uri.startswith("web://"):
                    return await self.read_web_resource(uri)
                elif uri.startswith("memory://"):
                    return await self.read_memory_resource(uri)
                else:
                    raise ValueError(f"Unknown resource URI: {uri}")
            except Exception as e:
                logger.error("Failed to read resource", uri=uri, error=str(e))
                raise HTTPException(status_code=404, detail=str(e))
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available tools"""
            return [
                Tool(
                    name="search_workspace",
                    description="Search indexed workspace content semantically",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "limit": {"type": "integer", "default": 10, "description": "Number of results"},
                            "file_types": {"type": "array", "items": {"type": "string"}, "description": "Filter by file types"}
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="crawl_website",
                    description="Crawl and index website content",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to crawl"},
                            "max_depth": {"type": "integer", "default": 3, "description": "Maximum crawl depth"},
                            "include_patterns": {"type": "array", "items": {"type": "string"}, "description": "URL patterns to include"}
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="store_memory",
                    description="Store information in persistent memory",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "key": {"type": "string", "description": "Memory key"},
                            "value": {"type": "string", "description": "Memory value"},
                            "tags": {"type": "array", "items": {"type": "string"}, "description": "Memory tags"}
                        },
                        "required": ["key", "value"]
                    }
                ),
                Tool(
                    name="retrieve_memory",
                    description="Retrieve information from persistent memory",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "key": {"type": "string", "description": "Memory key"},
                            "tags": {"type": "array", "items": {"type": "string"}, "description": "Filter by tags"}
                        }
                    }
                ),
                Tool(
                    name="index_workspace",
                    description="Manually trigger workspace indexing",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "path": {"type": "string", "description": "Specific path to index"},
                            "force": {"type": "boolean", "default": False, "description": "Force re-indexing"}
                        }
                    }
                ),
                Tool(
                    name="analyze_code_local",
                    description="Analyze code using local LM Studio model",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "code": {"type": "string", "description": "Code to analyze"},
                            "language": {"type": "string", "default": "python", "description": "Programming language"},
                            "analysis_type": {"type": "string", "default": "full", "description": "Type of analysis: full, security, performance"}
                        },
                        "required": ["code"]
                    }
                ),
                Tool(
                    name="generate_text_local",
                    description="Generate text using local LM Studio model",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "prompt": {"type": "string", "description": "Text prompt"},
                            "max_tokens": {"type": "integer", "default": 2048, "description": "Maximum tokens to generate"},
                            "temperature": {"type": "number", "default": 0.7, "description": "Generation temperature"},
                            "system_prompt": {"type": "string", "description": "System prompt for context"}
                        },
                        "required": ["prompt"]
                    }
                ),
                Tool(
                    name="summarize_content",
                    description="Summarize content using local AI",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "content": {"type": "string", "description": "Content to summarize"},
                            "max_length": {"type": "integer", "default": 200, "description": "Maximum summary length"}
                        },
                        "required": ["content"]
                    }
                ),
                Tool(
                    name="extract_keywords",
                    description="Extract keywords from text using local processing",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {"type": "string", "description": "Text to extract keywords from"},
                            "max_keywords": {"type": "integer", "default": 10, "description": "Maximum number of keywords"}
                        },
                        "required": ["text"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                # Ensure LLM is initialized
                if not self.llm_initialized:
                    await initialize_local_llm()
                    self.llm_initialized = True

                if name == "search_workspace":
                    return await self.search_workspace(**arguments)
                elif name == "crawl_website":
                    return await self.crawl_website(**arguments)
                elif name == "store_memory":
                    return await self.store_memory(**arguments)
                elif name == "retrieve_memory":
                    return await self.retrieve_memory(**arguments)
                elif name == "index_workspace":
                    return await self.index_workspace(**arguments)
                elif name == "analyze_code_local":
                    return await self.analyze_code_local(**arguments)
                elif name == "generate_text_local":
                    return await self.generate_text_local(**arguments)
                elif name == "summarize_content":
                    return await self.summarize_content(**arguments)
                elif name == "extract_keywords":
                    return await self.extract_keywords(**arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error("Tool execution failed", tool=name, error=str(e))
                return [TextContent(type="text", text=f"Error: {str(e)}")]
    
    async def search_workspace(self, query: str, limit: int = 10, file_types: Optional[List[str]] = None) -> List[TextContent]:
        """Search workspace content"""
        try:
            collection = self.chroma_client.get_collection("workspace")
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where={"file_type": {"$in": file_types}} if file_types else None
            )
            
            response = {
                "query": query,
                "results": results,
                "total_found": len(results["documents"][0]) if results["documents"] else 0
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
            
        except Exception as e:
            logger.error("Workspace search failed", error=str(e))
            return [TextContent(type="text", text=f"Search failed: {str(e)}")]
    
    async def crawl_website(self, url: str, max_depth: int = 3, include_patterns: Optional[List[str]] = None) -> List[TextContent]:
        """Crawl website content"""
        # Implementation would use the existing crawl4ai functionality
        return [TextContent(type="text", text=f"Crawling {url} with depth {max_depth}")]
    
    async def store_memory(self, key: str, value: str, tags: Optional[List[str]] = None) -> List[TextContent]:
        """Store memory"""
        try:
            memory_data = {
                "value": value,
                "tags": tags or [],
                "timestamp": asyncio.get_event_loop().time()
            }
            self.redis_client.set(f"memory:{key}", json.dumps(memory_data))
            return [TextContent(type="text", text=f"Stored memory for key: {key}")]
        except Exception as e:
            return [TextContent(type="text", text=f"Failed to store memory: {str(e)}")]
    
    async def retrieve_memory(self, key: Optional[str] = None, tags: Optional[List[str]] = None) -> List[TextContent]:
        """Retrieve memory"""
        try:
            if key:
                data = self.redis_client.get(f"memory:{key}")
                if data:
                    return [TextContent(type="text", text=data)]
                else:
                    return [TextContent(type="text", text=f"No memory found for key: {key}")]
            else:
                # Search by tags or return all
                keys = self.redis_client.keys("memory:*")
                memories = {}
                for k in keys:
                    memories[k.replace("memory:", "")] = self.redis_client.get(k)
                return [TextContent(type="text", text=json.dumps(memories, indent=2))]
        except Exception as e:
            return [TextContent(type="text", text=f"Failed to retrieve memory: {str(e)}")]
    
    async def index_workspace(self, path: Optional[str] = None, force: bool = False) -> List[TextContent]:
        """Index workspace"""
        return [TextContent(type="text", text=f"Indexing workspace path: {path or 'all'}")]
    
    async def read_workspace_resource(self, uri: str) -> str:
        """Read workspace resource"""
        return json.dumps({"uri": uri, "type": "workspace", "content": "Workspace content"})
    
    async def read_web_resource(self, uri: str) -> str:
        """Read web resource"""
        return json.dumps({"uri": uri, "type": "web", "content": "Web content"})
    
    async def read_memory_resource(self, uri: str) -> str:
        """Read memory resource"""
        return json.dumps({"uri": uri, "type": "memory", "content": "Memory content"})

    async def analyze_code_local(self, code: str, language: str = "python", analysis_type: str = "full") -> List[TextContent]:
        """Analyze code using local LM Studio model"""
        try:
            analysis = await local_llm.analyze_code(code, language)

            # Enhanced analysis with LM Studio
            if analysis_type == "security":
                prompt = f"""Perform a security analysis of this {language} code:

{code}

Focus on:
1. Security vulnerabilities
2. Input validation issues
3. Authentication/authorization problems
4. Data exposure risks
5. Injection attack vectors"""

                security_analysis = await local_llm.generate_text(prompt, max_tokens=1024)
                analysis["security_analysis"] = security_analysis

            elif analysis_type == "performance":
                prompt = f"""Analyze the performance characteristics of this {language} code:

{code}

Focus on:
1. Time complexity
2. Space complexity
3. Bottlenecks
4. Optimization opportunities
5. Scalability concerns"""

                performance_analysis = await local_llm.generate_text(prompt, max_tokens=1024)
                analysis["performance_analysis"] = performance_analysis

            return [TextContent(type="text", text=json.dumps(analysis, indent=2))]

        except Exception as e:
            logger.error("Code analysis failed", error=str(e))
            return [TextContent(type="text", text=f"Code analysis failed: {str(e)}")]

    async def generate_text_local(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.7, system_prompt: str = None) -> List[TextContent]:
        """Generate text using local LM Studio model"""
        try:
            result = await local_llm.generate_text(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                system_prompt=system_prompt
            )

            return [TextContent(type="text", text=result)]

        except Exception as e:
            logger.error("Text generation failed", error=str(e))
            return [TextContent(type="text", text=f"Text generation failed: {str(e)}")]

    async def summarize_content(self, content: str, max_length: int = 200) -> List[TextContent]:
        """Summarize content using local AI"""
        try:
            summary = await local_llm.summarize_text(content, max_length)

            # Also extract keywords for additional context
            keywords = await local_llm.extract_keywords(content, max_keywords=5)

            result = {
                "summary": summary,
                "keywords": keywords,
                "original_length": len(content),
                "summary_length": len(summary),
                "compression_ratio": f"{len(summary)/len(content)*100:.1f}%"
            }

            return [TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error("Content summarization failed", error=str(e))
            return [TextContent(type="text", text=f"Summarization failed: {str(e)}")]

    async def extract_keywords(self, text: str, max_keywords: int = 10) -> List[TextContent]:
        """Extract keywords from text using local processing"""
        try:
            keywords = await local_llm.extract_keywords(text, max_keywords)

            result = {
                "keywords": keywords,
                "text_length": len(text),
                "word_count": len(text.split()),
                "keyword_density": f"{len(keywords)/len(text.split())*100:.1f}%"
            }

            return [TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error("Keyword extraction failed", error=str(e))
            return [TextContent(type="text", text=f"Keyword extraction failed: {str(e)}")]

async def main():
    """Main entry point"""
    server_instance = EnhancedMCPServer()
    
    # Health check endpoint
    @server_instance.app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "crawl4ai-enhanced-mcp"}
    
    # Start both MCP server and FastAPI
    if len(sys.argv) > 1 and sys.argv[1] == "--stdio":
        # Run as MCP server
        async with stdio_server() as (read_stream, write_stream):
            await server_instance.server.run(
                read_stream,
                write_stream,
                server_instance.server.create_initialization_options()
            )
    else:
        # Run as web server
        config = uvicorn.Config(
            server_instance.app,
            host="0.0.0.0",
            port=int(os.getenv("MCP_SERVER_PORT", "8978")),
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

if __name__ == "__main__":
    asyncio.run(main())
