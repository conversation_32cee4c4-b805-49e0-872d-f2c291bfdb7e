version: '3.8'

services:
  # ChromaDB Vector Database
  chromadb:
    image: chromadb/chroma:latest
    container_name: crawl4ai-chromadb
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=["*"]
    volumes:
      - chromadb_data:/chroma/chroma
    ports:
      - "8000:8000"
    networks:
      - crawl4ai-network
    restart: unless-stopped

  # Redis Cache for MCP operations
  redis:
    image: redis:7-alpine
    container_name: crawl4ai-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - crawl4ai-network
    restart: unless-stopped

  # Main Crawl4AI Agent with MCP Server
  crawl4ai-agent:
    build:
      context: .
      dockerfile: Dockerfile.enhanced
    container_name: crawl4ai-agent-v2
    environment:
      # Local LM Studio Configuration (Primary)
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - USE_LOCAL_LLM=true
      - LOCAL_LLM_FALLBACK=true
      - MODEL_CHOICE=local-gemma3-4b
      - LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2

      # Optional External APIs (for web research only)
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - TAVILY_API_KEY=${TAVILY_API_KEY:-}
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY:-}

      # Database Configuration
      - CHROMA_DB_HOST=chromadb
      - CHROMA_DB_PORT=8000
      - REDIS_HOST=redis
      - REDIS_PORT=6379

      # MCP Server Configuration
      - MCP_SERVER_PORT=8978
      - WORKSPACE_ROOT=/workspace

      # Local Processing Configuration
      - ENABLE_LOCAL_PROCESSING=true
      - OFFLINE_MODE=true
      - LOCAL_CACHE_SIZE=1000

      # Performance Configuration
      - MAX_CONCURRENT_CRAWLS=5
      - CHUNK_SIZE=1000
      - BATCH_SIZE=50
    volumes:
      - ./chroma_db:/app/chroma_db
      - ./data:/app/data
      - /home/<USER>/HVAC/unifikacja:/workspace:ro
      - ./logs:/app/logs
    ports:
      - "8501:8501"  # Streamlit
      - "8978:8978"  # MCP Server
      - "8080:8080"  # Web API
    depends_on:
      - chromadb
      - redis
    networks:
      - crawl4ai-network
    restart: unless-stopped
    command: python mcp_server.py

  # Workspace Indexer Service
  workspace-indexer:
    build:
      context: .
      dockerfile: Dockerfile.indexer
    container_name: crawl4ai-workspace-indexer
    environment:
      - CHROMA_DB_HOST=chromadb
      - CHROMA_DB_PORT=8000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - WORKSPACE_ROOT=/workspace
      - INDEXING_INTERVAL=300  # 5 minutes
    volumes:
      - /home/<USER>/HVAC/unifikacja:/workspace:ro
      - ./logs:/app/logs
    depends_on:
      - chromadb
      - redis
    networks:
      - crawl4ai-network
    restart: unless-stopped

  # Web Crawler Service
  web-crawler:
    build:
      context: .
      dockerfile: Dockerfile.crawler
    container_name: crawl4ai-web-crawler
    environment:
      - CHROMA_DB_HOST=chromadb
      - CHROMA_DB_PORT=8000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - chromadb
      - redis
    networks:
      - crawl4ai-network
    restart: unless-stopped

  # Streamlit Dashboard
  streamlit-dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    container_name: crawl4ai-dashboard
    environment:
      - CHROMA_DB_HOST=chromadb
      - CHROMA_DB_PORT=8000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MCP_SERVER_HOST=crawl4ai-agent
      - MCP_SERVER_PORT=8978
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "8502:8501"  # Streamlit Dashboard
    depends_on:
      - crawl4ai-agent
      - chromadb
      - redis
    networks:
      - crawl4ai-network
    restart: unless-stopped
    command: streamlit run dashboard.py --server.address 0.0.0.0 --server.port 8501

volumes:
  chromadb_data:
    driver: local
  redis_data:
    driver: local

networks:
  crawl4ai-network:
    driver: bridge
